<?php $__env->startSection('title', 'Archives'); ?>

<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/pages/students.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/pages/archived-students.css')); ?>">
    <style>
        /* Remarks Badge Styling */
        .remarks-badge.inactive-remarks {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .remarks-badge.masterlist-remarks {
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        /* Modal Styling */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .modal-header h2 {
            margin: 0;
            color: #1e5631;
            font-size: 24px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
            transition: color 0.3s;
        }

        .close:hover {
            color: #000;
        }

        .modal-body {
            padding: 20px;
        }

        .loading, .error {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading i, .error i {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }

        .error {
            color: #dc3545;
        }

        .student-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .detail-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .detail-section h3 {
            margin: 0 0 16px 0;
            color: #1e5631;
            font-size: 18px;
            border-bottom: 2px solid #1e5631;
            padding-bottom: 8px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-row .label {
            font-weight: 600;
            color: #495057;
            flex: 0 0 40%;
        }

        .detail-row .value {
            flex: 1;
            text-align: right;
            color: #212529;
        }

        .detail-row .value.badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .detail-row .value.badge.masterlist {
            background-color: #28a745;
            color: white;
        }

        .detail-row .value.badge.inactive {
            background-color: #dc3545;
            color: white;
        }

        .detail-row .value.remarks.inactive {
            color: #dc3545;
            font-weight: 500;
        }

        .detail-row .value.remarks.masterlist {
            color: #28a745;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .student-details-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .modal-content {
                width: 95%;
                margin: 10px;
            }

            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .detail-row .value {
                text-align: left;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [['title' => 'Archives', 'active' => true]]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([['title' => 'Archives', 'active' => true]])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="dashboard-header">
        <h1>Archives</h1>
        <div class="date"><?php echo e(date('F d, Y')); ?></div>
    </div>

    <!-- Archive Categories -->
    <div class="student-categories">
        <div class="category-tabs">
            <div class="tab-group">
                <button class="tab-btn active" onclick="showArchiveCategory('masterlist', this)">Masterlist</button>
                <button class="tab-btn" onclick="showArchiveCategory('inactive', this)">Inactive</button>
            </div>
            <div class="archive-actions">
                <button class="export-btn" onclick="exportArchivedStudents()">
                    <i class="fas fa-download"></i> Export
                </button>
            </div>
        </div>
    </div>

    <!-- Archived Grantees Table -->
    <div class="student-table-container">
        <div class="table-header" style="display: none;">
            <h3 id="archiveTableTitle"></h3>
        </div>
        <table class="students-table">
            <thead>
                <tr>
                    <th>Grantee ID</th>
                    <th>Name</th>
                    <th>Benefactor Type</th>
                    <th>Remarks</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="archivedStudentsTableBody">
                <?php $__empty_1 = true; $__currentLoopData = $archivedStudents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr data-year="<?php echo e($student->archived_academic_year); ?>" data-semester="<?php echo e($student->archived_semester); ?>"
                        data-type="<?php echo e($student->scholarship_type); ?>"
                        data-archive-type="<?php echo e($student->archive_type ?? 'masterlist'); ?>"
                        data-search="<?php echo e(strtolower($student->first_name . ' ' . $student->last_name . ' ' . $student->student_id)); ?>">
                        <td><?php echo e($student->student_id); ?></td>
                        <td><?php echo e($student->first_name); ?> <?php echo e($student->last_name); ?></td>
                        <td>
                            <span class="benefactor-badge">
                                <?php echo e(ucfirst($student->scholarship_type)); ?>

                            </span>
                        </td>
                        <td>
                            <span class="remarks-badge <?php echo e($student->archive_type === 'inactive' ? 'inactive-remarks' : 'masterlist-remarks'); ?>">
                                <?php if($student->archive_type === 'inactive'): ?>
                                    <?php echo e($student->remarks ?: 'No specific reason'); ?>

                                <?php else: ?>
                                    <?php echo e($student->archive_type === 'masterlist' ? 'Semester/Year End Archive' : ($student->remarks ?: 'N/A')); ?>

                                <?php endif; ?>
                            </span>
                        </td>
                        <td>
                            <button class="action-btn view" onclick="viewArchivedStudent(<?php echo e($student->id); ?>)"
                                title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="5" class="empty-state">
                            No archived grantees found.
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Archived Student Details Modal -->
    <div id="archivedStudentModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Archived Student Details</h2>
                <span class="close" onclick="closeArchivedStudentModal()">&times;</span>
            </div>
            <div class="modal-body" id="archivedStudentDetails">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading student details...</p>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Initialize navigation to ensure proper link behavior
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            // Initialize masterlist tab as active on page load
            showArchiveCategory('masterlist', document.querySelector('.tab-btn.active'));
        });

        function initializeNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // Allow default link behavior
                    return true;
                });
            });
        }

        function filterArchives() {
            const yearFilter = document.getElementById('yearFilter').value;
            const semesterFilter = document.getElementById('semesterFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

            const rows = document.querySelectorAll('#archivedStudentsTableBody tr[data-year]');

            rows.forEach(row => {
                const year = row.dataset.year;
                const semester = row.dataset.semester;
                const type = row.dataset.type;
                const searchText = row.dataset.search;

                let show = true;

                if (yearFilter && year !== yearFilter) show = false;
                if (semesterFilter && semester !== semesterFilter) show = false;
                if (typeFilter && type !== typeFilter) show = false;
                if (searchFilter && !searchText.includes(searchFilter)) show = false;

                row.style.display = show ? '' : 'none';
            });
        }

        function viewArchivedStudent(studentId) {
            console.log('Viewing archived grantee:', studentId);

            // Show modal
            document.getElementById('archivedStudentModal').style.display = 'block';

            // Show loading state
            document.getElementById('archivedStudentDetails').innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading student details...</p>
                </div>
            `;

            // Fetch student details
            fetch(`/admin/archived-students/${studentId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayArchivedStudentDetails(data.student);
                    } else {
                        document.getElementById('archivedStudentDetails').innerHTML = `
                            <div class="error">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Error loading student details: ${data.message}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('archivedStudentDetails').innerHTML = `
                        <div class="error">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Error loading student details. Please try again.</p>
                        </div>
                    `;
                });
        }

        function displayArchivedStudentDetails(student) {
            const detailsHtml = `
                <div class="student-details-grid">
                    <div class="detail-section">
                        <h3>Personal Information</h3>
                        <div class="detail-row">
                            <span class="label">Student ID:</span>
                            <span class="value">${student.student_id}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Full Name:</span>
                            <span class="value">${student.first_name} ${student.last_name}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Email:</span>
                            <span class="value">${student.email || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Contact Number:</span>
                            <span class="value">${student.contact_number || 'N/A'}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>Academic Information</h3>
                        <div class="detail-row">
                            <span class="label">Course:</span>
                            <span class="value">${student.course || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Department:</span>
                            <span class="value">${student.department || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Year Level:</span>
                            <span class="value">${student.year_level || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">GWA:</span>
                            <span class="value">${student.gwa || 'N/A'}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>Scholarship Information</h3>
                        <div class="detail-row">
                            <span class="label">Application ID:</span>
                            <span class="value">${student.original_application_id}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Scholarship Type:</span>
                            <span class="value">${student.scholarship_type}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Archive Type:</span>
                            <span class="value badge ${student.archive_type}">${student.archive_type.charAt(0).toUpperCase() + student.archive_type.slice(1)}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>Archive Information</h3>
                        <div class="detail-row">
                            <span class="label">Archived Semester:</span>
                            <span class="value">${student.archived_semester}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Archived Academic Year:</span>
                            <span class="value">${student.archived_academic_year}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Archived Date:</span>
                            <span class="value">${new Date(student.archived_at).toLocaleDateString()}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Archived By:</span>
                            <span class="value">${student.archived_by || 'System'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Remarks:</span>
                            <span class="value remarks ${student.archive_type}">
                                ${student.archive_type === 'inactive'
                                    ? (student.remarks || 'No specific reason')
                                    : (student.archive_type === 'masterlist' ? 'Semester/Year End Archive' : (student.remarks || 'N/A'))
                                }
                            </span>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('archivedStudentDetails').innerHTML = detailsHtml;
        }

        function closeArchivedStudentModal() {
            document.getElementById('archivedStudentModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('archivedStudentModal');
            if (event.target === modal) {
                closeArchivedStudentModal();
            }
        }

        function exportArchivedStudents() {
            // Get the currently active tab to determine export type
            const activeTab = document.querySelector('.tab-btn.active');
            const archiveType = activeTab.textContent.toLowerCase().trim();

            // Build export URL with type parameter
            let exportUrl = '<?php echo e(route('admin.archived-students.export')); ?>';
            if (archiveType !== 'all') {
                exportUrl += '?type=' + archiveType;
            }

            // Trigger download
            window.location.href = exportUrl;
        }

        function showArchiveCategory(category, button) {
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            button.classList.add('active');

            // Filter table rows based on category (no title update for cleaner look)
            const rows = document.querySelectorAll('#archivedStudentsTableBody tr');
            rows.forEach(row => {
                const archiveType = row.dataset.archiveType;
                if (category === 'all' || archiveType === category) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = event.currentTarget.parentElement;
            const menu = dropdown.querySelector('.dropdown-menu');
            const arrow = dropdown.querySelector('.dropdown-arrow');

            dropdown.classList.toggle('open');

            if (dropdown.classList.contains('open')) {
                menu.style.maxHeight = menu.scrollHeight + 'px';
                arrow.style.transform = 'rotate(180deg)';
            } else {
                menu.style.maxHeight = '0';
                arrow.style.transform = 'rotate(0deg)';
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive - St. Paul University Philippines\Desktop\CAPSTONE\System\Thesis\resources\views/admin/archived-students.blade.php ENDPATH**/ ?>